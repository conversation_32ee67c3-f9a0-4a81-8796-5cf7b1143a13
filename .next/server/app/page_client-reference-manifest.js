globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./src/components/layout/Footer.js":{"*":{"id":"(ssr)/./src/components/layout/Footer.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navbar.js":{"*":{"id":"(ssr)/./src/components/layout/Navbar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/About.js":{"*":{"id":"(ssr)/./src/components/sections/About.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Benefits.js":{"*":{"id":"(ssr)/./src/components/sections/Benefits.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Contact.js":{"*":{"id":"(ssr)/./src/components/sections/Contact.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.js":{"*":{"id":"(ssr)/./src/components/sections/Hero.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Services.js":{"*":{"id":"(ssr)/./src/components/sections/Services.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Team.js":{"*":{"id":"(ssr)/./src/components/sections/Team.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/TrustLogos.js":{"*":{"id":"(ssr)/./src/components/sections/TrustLogos.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src/app/layout.js\",\"import\":\"Roboto\",\"arguments\":[{\"variable\":\"--font-roboto\",\"subsets\":[\"latin\"],\"weight\":[\"400\",\"500\",\"700\"],\"display\":\"swap\"}],\"variableName\":\"roboto\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/layout/Footer.js":{"id":"(app-pages-browser)/./src/components/layout/Footer.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/layout/Navbar.js":{"id":"(app-pages-browser)/./src/components/layout/Navbar.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/About.js":{"id":"(app-pages-browser)/./src/components/sections/About.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Benefits.js":{"id":"(app-pages-browser)/./src/components/sections/Benefits.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Contact.js":{"id":"(app-pages-browser)/./src/components/sections/Contact.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Hero.js":{"id":"(app-pages-browser)/./src/components/sections/Hero.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Services.js":{"id":"(app-pages-browser)/./src/components/sections/Services.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/Team.js":{"id":"(app-pages-browser)/./src/components/sections/Team.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/components/sections/TrustLogos.js":{"id":"(app-pages-browser)/./src/components/sections/TrustLogos.js","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false}},"entryCSSFiles":{"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/":[],"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"/Users/<USER>/Projects/LeeXLoyalist/MizuFlow/mizu-landing/src/app/page":[]},"rscModuleMapping":{"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Footer.js":{"*":{"id":"(rsc)/./src/components/layout/Footer.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/layout/Navbar.js":{"*":{"id":"(rsc)/./src/components/layout/Navbar.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/About.js":{"*":{"id":"(rsc)/./src/components/sections/About.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Benefits.js":{"*":{"id":"(rsc)/./src/components/sections/Benefits.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Contact.js":{"*":{"id":"(rsc)/./src/components/sections/Contact.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Hero.js":{"*":{"id":"(rsc)/./src/components/sections/Hero.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Services.js":{"*":{"id":"(rsc)/./src/components/sections/Services.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/Team.js":{"*":{"id":"(rsc)/./src/components/sections/Team.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/sections/TrustLogos.js":{"*":{"id":"(rsc)/./src/components/sections/TrustLogos.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}