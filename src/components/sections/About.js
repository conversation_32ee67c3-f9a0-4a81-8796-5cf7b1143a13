"use client";

import { motion } from "framer-motion";
import { Lightbulb, Users, Award } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const pillars = [
  {
    icon: Lightbulb,
    title: "Innovation",
    description: "We constantly explore cutting-edge technologies to create forward-thinking solutions that push the boundaries of what's possible in financial automation.",
    color: "from-yellow-500 to-orange-500"
  },
  {
    icon: Users,
    title: "Collaboration",
    description: "We work closely with clients to understand their unique needs and challenges, ensuring our solutions are perfectly tailored to their business requirements.",
    color: "from-blue-500 to-teal-500"
  },
  {
    icon: Award,
    title: "Excellence",
    description: "We are committed to delivering the highest quality solutions with measurable results, maintaining the highest standards in everything we do.",
    color: "from-purple-500 to-pink-500"
  }
];

export default function About() {
  return (
    <section id="about" className="py-24 bg-mist-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-ocean-900 mb-6">
            About MizuFlow
          </h2>
          <div className="max-w-4xl mx-auto">
            <p className="text-xl text-ocean-600 leading-relaxed mb-8">
              MizuFlow is a trusted provider of financial automation and business intelligence solutions. 
              We combine cutting-edge technology with deep financial expertise to help businesses 
              streamline operations, improve accuracy, and make data-driven decisions.
            </p>
            <p className="text-lg text-ocean-600 leading-relaxed">
              Our team of financial experts and technology specialists work together to deliver 
              tailored solutions that address your unique business challenges and drive sustainable growth.
            </p>
          </div>
        </motion.div>

        {/* Company Image Placeholder */}
        <motion.div
          className="mb-16 rounded-2xl overflow-hidden shadow-2xl"
          initial={{ opacity: 0, scale: 0.9 }}
          whileInView={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="relative h-64 md:h-96 bg-gradient-ocean-teal flex items-center justify-center">
            <div className="absolute inset-0 bg-black/20" />
            <div className="relative z-10 text-center text-white">
              <h3 className="text-2xl md:text-3xl font-bold mb-4">
                Empowering Financial Excellence
              </h3>
              <p className="text-lg md:text-xl opacity-90 max-w-2xl mx-auto px-4">
                Through innovative technology and expert guidance, we transform 
                how businesses manage their financial operations.
              </p>
            </div>
            
            {/* Decorative Elements */}
            <motion.div
              className="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full blur-xl"
              animate={{
                scale: [1, 1.2, 1],
                opacity: [0.3, 0.6, 0.3],
              }}
              transition={{
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
            <motion.div
              className="absolute bottom-10 right-10 w-32 h-32 bg-teal-400/20 rounded-full blur-2xl"
              animate={{
                scale: [1, 0.8, 1],
                opacity: [0.4, 0.7, 0.4],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "easeInOut",
                delay: 2,
              }}
            />
          </div>
        </motion.div>

        {/* Company Pillars */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pillars.map((pillar, index) => (
            <motion.div
              key={pillar.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-xl transition-all duration-300 group border-0 bg-white">
                <CardHeader className="text-center pb-4">
                  <motion.div
                    className={`mx-auto mb-4 p-4 bg-gradient-to-r ${pillar.color} rounded-full w-16 h-16 flex items-center justify-center`}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <pillar.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  <CardTitle className="text-2xl font-bold text-ocean-900 group-hover:text-teal-500 transition-colors">
                    {pillar.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <CardDescription className="text-ocean-600 text-base leading-relaxed">
                    {pillar.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Stats Section */}
        <motion.div
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div>
            <div className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2">5+</div>
            <div className="text-ocean-600">Years Experience</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2">100+</div>
            <div className="text-ocean-600">Projects Completed</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2">50+</div>
            <div className="text-ocean-600">Happy Clients</div>
          </div>
          <div>
            <div className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2">24/7</div>
            <div className="text-ocean-600">Support Available</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
