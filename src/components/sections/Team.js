"use client";

import { motion } from "framer-motion";
import { Linkedin, Mail } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const teamMembers = [
  {
    name: "<PERSON>",
    role: "Chief Executive Officer",
    bio: "<PERSON> brings over 15 years of experience in financial technology and business automation. He leads MizuFlow's strategic vision and oversees the development of innovative solutions that transform how businesses manage their financial operations.",
    image: "/api/placeholder/300/300", // Placeholder - replace with actual image
    linkedin: "#",
    email: "<EMAIL>"
  },
  {
    name: "<PERSON><PERSON>",
    role: "Chief Technology Officer",
    bio: "<PERSON><PERSON> is a technology visionary with expertise in AI, machine learning, and cloud computing. He architects MizuFlow's technical infrastructure and drives the development of cutting-edge automation solutions.",
    image: "/api/placeholder/300/300", // Placeholder - replace with actual image
    linkedin: "#",
    email: "<EMAIL>"
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Head of Operations",
    bio: "<PERSON><PERSON><PERSON> specializes in process optimization and client success. He ensures seamless implementation of <PERSON><PERSON><PERSON><PERSON>'s solutions and maintains the highest standards of service delivery for our clients.",
    image: "/api/placeholder/300/300", // Placeholder - replace with actual image
    linkedin: "#",
    email: "<EMAIL>"
  }
];

export default function Team() {
  return (
    <section id="team" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-ocean-900 mb-4">
            Meet Our Team
          </h2>
          <p className="text-xl text-ocean-600 max-w-3xl mx-auto">
            Our experienced team of financial experts and technology specialists 
            are dedicated to delivering exceptional results for your business.
          </p>
        </motion.div>

        {/* Team Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-xl transition-all duration-300 group border-0 bg-white overflow-hidden">
                <CardContent className="p-0">
                  {/* Profile Image */}
                  <div className="relative overflow-hidden">
                    <motion.div
                      className="w-full h-80 bg-gradient-ocean-teal flex items-center justify-center"
                      whileHover={{ scale: 1.05 }}
                      transition={{ duration: 0.3 }}
                    >
                      {/* Placeholder for actual image */}
                      <div className="w-32 h-32 bg-white/20 rounded-full flex items-center justify-center">
                        <div className="text-4xl font-bold text-white">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </div>
                      </div>
                    </motion.div>
                    
                    {/* Social Links Overlay */}
                    <motion.div
                      className="absolute inset-0 bg-black/50 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      initial={{ opacity: 0 }}
                      whileHover={{ opacity: 1 }}
                    >
                      <div className="flex space-x-4">
                        <motion.a
                          href={member.linkedin}
                          className="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Linkedin className="h-5 w-5 text-white" />
                        </motion.a>
                        <motion.a
                          href={`mailto:${member.email}`}
                          className="p-3 bg-white/20 rounded-full hover:bg-white/30 transition-colors"
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                        >
                          <Mail className="h-5 w-5 text-white" />
                        </motion.a>
                      </div>
                    </motion.div>
                  </div>

                  {/* Member Info */}
                  <div className="p-6">
                    <h3 className="text-2xl font-bold text-ocean-900 mb-2 group-hover:text-teal-500 transition-colors">
                      {member.name}
                    </h3>
                    <p className="text-teal-500 font-semibold mb-4">
                      {member.role}
                    </p>
                    <p className="text-ocean-600 leading-relaxed">
                      {member.bio}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Team CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <div className="bg-gradient-ocean-teal rounded-2xl p-8 text-white">
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Want to Join Our Team?
            </h3>
            <p className="text-white/90 mb-6 max-w-2xl mx-auto">
              We're always looking for talented individuals who share our passion 
              for innovation and excellence in financial technology.
            </p>
            <motion.button
              className="bg-white text-ocean-900 px-8 py-3 rounded-lg font-semibold hover:bg-white/90 transition-colors"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => window.open("mailto:<EMAIL>", "_blank")}
            >
              View Open Positions
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
