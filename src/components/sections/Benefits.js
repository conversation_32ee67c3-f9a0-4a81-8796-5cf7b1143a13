"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON>3, Shield } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const benefits = [
  {
    icon: Brain,
    title: "Intelligent Automation",
    description: "Leverage AI and machine learning to automate repetitive financial tasks, reducing manual effort and minimizing errors.",
    features: [
      "AI-powered invoice processing",
      "Automated data entry",
      "Smart reconciliation",
      "Predictive analytics"
    ]
  },
  {
    icon: BarChart3,
    title: "Data-Driven Insights",
    description: "Transform raw financial data into actionable business intelligence to make informed strategic decisions.",
    features: [
      "Real-time dashboards",
      "Custom reporting",
      "Trend analysis",
      "Performance metrics"
    ]
  },
  {
    icon: Shield,
    title: "Enhanced Accuracy",
    description: "Improve data accuracy and compliance with automated validation and reconciliation processes.",
    features: [
      "Error detection",
      "Compliance monitoring",
      "Audit trails",
      "Quality assurance"
    ]
  }
];

export default function Benefits() {
  return (
    <section className="py-24 bg-mist-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-ocean-900 mb-4">
            Why Choose MizuFlow
          </h2>
          <p className="text-xl text-ocean-600 max-w-3xl mx-auto">
            We combine cutting-edge technology with financial expertise to deliver 
            solutions that drive efficiency and growth.
          </p>
        </motion.div>

        {/* Benefits Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {benefits.map((benefit, index) => (
            <motion.div
              key={benefit.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="h-full hover:shadow-xl transition-all duration-300 group border-0 bg-white">
                <CardHeader className="text-center pb-4">
                  <motion.div
                    className="mx-auto mb-4 p-4 bg-gradient-ocean-teal rounded-full w-16 h-16 flex items-center justify-center"
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <benefit.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  <CardTitle className="text-2xl font-bold text-ocean-900 group-hover:text-teal-500 transition-colors">
                    {benefit.title}
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center">
                  <CardDescription className="text-ocean-600 mb-6 text-base leading-relaxed">
                    {benefit.description}
                  </CardDescription>
                  
                  {/* Feature List */}
                  <ul className="space-y-2 text-left">
                    {benefit.features.map((feature, featureIndex) => (
                      <motion.li
                        key={feature}
                        className="flex items-center text-ocean-700"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ 
                          duration: 0.5, 
                          delay: (index * 0.2) + (featureIndex * 0.1) + 0.5 
                        }}
                        viewport={{ once: true }}
                      >
                        <div className="w-2 h-2 bg-teal-500 rounded-full mr-3 flex-shrink-0" />
                        {feature}
                      </motion.li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom CTA */}
        <motion.div
          className="text-center mt-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <p className="text-lg text-ocean-600 mb-6">
            Ready to transform your financial operations?
          </p>
          <motion.button
            className="bg-gradient-ocean-teal text-white px-8 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              const element = document.querySelector("#contact");
              if (element) {
                element.scrollIntoView({ behavior: "smooth" });
              }
            }}
          >
            Get Started Today
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
