"use client";

import { motion } from "framer-motion";
import { 
  FileText, 
  Cloud, 
  BarChart, 
  Monitor, 
  Cog, 
  MessageSquare 
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const services = [
  {
    icon: FileText,
    title: "Invoice Automation",
    description: "Automate email monitoring and SharePoint integration, extract invoice data using Azure Form Recognizer, and streamline accounting system entries with human validation.",
    features: [
      "Email monitoring automation",
      "SharePoint integration",
      "Azure Form Recognizer",
      "Human validation workflow"
    ],
    color: "from-blue-500 to-blue-600"
  },
  {
    icon: Cloud,
    title: "Cloud-Based AI Forecasting",
    description: "Utilize AI frameworks such as AWS AutoGluon and other cloud-based solutions for automated financial predictions and enhanced forecasting accuracy.",
    features: [
      "AWS AutoGluon integration",
      "Automated predictions",
      "Machine learning models",
      "Real-time forecasting"
    ],
    color: "from-teal-500 to-teal-600"
  },
  {
    icon: <PERSON><PERSON><PERSON>,
    title: "Financial Statement & MD&A Automation",
    description: "Integrate data from accounting systems to generate automated reports using Power BI and Power Automate for comprehensive financial reporting.",
    features: [
      "Power BI integration",
      "Power Automate workflows",
      "Automated report generation",
      "MD&A automation"
    ],
    color: "from-purple-500 to-purple-600"
  },
  {
    icon: Monitor,
    title: "Power BI Implementation",
    description: "Develop interactive dashboards, implement self-service BI, and optimize data models for enhanced business intelligence and decision-making.",
    features: [
      "Interactive dashboards",
      "Self-service BI",
      "Data model optimization",
      "Custom visualizations"
    ],
    color: "from-orange-500 to-orange-600"
  },
  {
    icon: Cog,
    title: "Process Automation",
    description: "Automate manual data entry tasks, implement Power Automate workflows, and enhance SharePoint functionality with user-driven automation buttons.",
    features: [
      "Manual data entry automation",
      "Power Automate workflows",
      "SharePoint enhancement",
      "User-driven automation"
    ],
    color: "from-green-500 to-green-600"
  },
  {
    icon: MessageSquare,
    title: "Variance Analysis using NLP & RAG",
    description: "Leverage Natural Language Processing (NLP), Retrieval-Augmented Generation (RAG), and Large Language Models (LLMs) to automate variance explanations in financial reporting.",
    features: [
      "Natural Language Processing",
      "Retrieval-Augmented Generation",
      "Large Language Models",
      "Automated variance explanations"
    ],
    color: "from-red-500 to-red-600"
  }
];

export default function Services() {
  return (
    <section id="services" className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-16"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-ocean-900 mb-4">
            Our Services
          </h2>
          <p className="text-xl text-ocean-600 max-w-3xl mx-auto">
            Comprehensive financial automation and business intelligence solutions 
            tailored to your needs.
          </p>
        </motion.div>

        {/* Services Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="group"
            >
              <Card className="h-full hover:shadow-2xl transition-all duration-300 border-0 bg-white overflow-hidden">
                <CardHeader className="relative pb-4">
                  {/* Gradient Background */}
                  <div className={`absolute top-0 left-0 right-0 h-2 bg-gradient-to-r ${service.color}`} />
                  
                  <motion.div
                    className={`mx-auto mb-4 p-4 bg-gradient-to-r ${service.color} rounded-full w-16 h-16 flex items-center justify-center`}
                    whileHover={{ scale: 1.1, rotate: 5 }}
                    transition={{ type: "spring", stiffness: 400, damping: 10 }}
                  >
                    <service.icon className="h-8 w-8 text-white" />
                  </motion.div>
                  
                  <CardTitle className="text-xl font-bold text-ocean-900 group-hover:text-teal-500 transition-colors text-center">
                    {service.title}
                  </CardTitle>
                </CardHeader>
                
                <CardContent>
                  <CardDescription className="text-ocean-600 mb-6 text-base leading-relaxed">
                    {service.description}
                  </CardDescription>
                  
                  {/* Feature List */}
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <motion.li
                        key={feature}
                        className="flex items-center text-ocean-700 text-sm"
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        transition={{ 
                          duration: 0.5, 
                          delay: (index * 0.1) + (featureIndex * 0.05) + 0.3 
                        }}
                        viewport={{ once: true }}
                      >
                        <div className="w-1.5 h-1.5 bg-teal-500 rounded-full mr-3 flex-shrink-0" />
                        {feature}
                      </motion.li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Bottom Section */}
        <motion.div
          className="text-center mt-16 p-8 bg-gradient-ocean-teal rounded-2xl"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
        >
          <h3 className="text-2xl md:text-3xl font-bold text-white mb-4">
            Ready to Automate Your Financial Processes?
          </h3>
          <p className="text-white/90 mb-6 max-w-2xl mx-auto">
            Let us help you streamline your operations and unlock the power of intelligent automation.
          </p>
          <motion.button
            className="bg-white text-ocean-900 px-8 py-3 rounded-lg font-semibold hover:bg-white/90 transition-colors"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => {
              const element = document.querySelector("#contact");
              if (element) {
                element.scrollIntoView({ behavior: "smooth" });
              }
            }}
          >
            Schedule a Consultation
          </motion.button>
        </motion.div>
      </div>
    </section>
  );
}
