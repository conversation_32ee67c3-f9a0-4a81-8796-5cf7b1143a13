"use client";

import { motion } from "framer-motion";

// Placeholder company logos - replace with actual client logos
const companies = [
  { name: "Microsoft", logo: "MS" },
  { name: "Amazon", logo: "AWS" },
  { name: "Google", logo: "G<PERSON>" },
  { name: "Salesforce", logo: "SF" },
  { name: "Oracle", logo: "ORC" },
  { name: "SAP", logo: "SAP" },
  { name: "IBM", logo: "IBM" },
  { name: "Adobe", logo: "ADB" }
];

export default function TrustLogos() {
  return (
    <section className="py-16 bg-white border-b border-gray-100">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <p className="text-ocean-600 font-medium mb-4">
            Trusted by leading companies worldwide
          </p>
          <h3 className="text-2xl md:text-3xl font-bold text-ocean-900">
            Powering Financial Excellence for Industry Leaders
          </h3>
        </motion.div>

        {/* Logos Grid */}
        <div className="relative overflow-hidden">
          {/* Gradient Overlays */}
          <div className="absolute left-0 top-0 bottom-0 w-20 bg-gradient-to-r from-white to-transparent z-10" />
          <div className="absolute right-0 top-0 bottom-0 w-20 bg-gradient-to-l from-white to-transparent z-10" />
          
          {/* Scrolling Container */}
          <motion.div
            className="flex space-x-12 md:space-x-16"
            animate={{
              x: [0, -1920], // Adjust based on total width
            }}
            transition={{
              x: {
                repeat: Infinity,
                repeatType: "loop",
                duration: 30,
                ease: "linear",
              },
            }}
          >
            {/* First set of logos */}
            {companies.map((company, index) => (
              <motion.div
                key={`first-${company.name}`}
                className="flex-shrink-0 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-ocean-600 to-teal-500 rounded-lg flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <span className="text-white font-bold text-lg md:text-xl">
                    {company.logo}
                  </span>
                </div>
              </motion.div>
            ))}
            
            {/* Duplicate set for seamless loop */}
            {companies.map((company, index) => (
              <motion.div
                key={`second-${company.name}`}
                className="flex-shrink-0 flex items-center justify-center"
                initial={{ opacity: 0, scale: 0.8 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                viewport={{ once: true }}
              >
                <div className="w-24 h-24 md:w-32 md:h-32 bg-gradient-to-br from-ocean-600 to-teal-500 rounded-lg flex items-center justify-center shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <span className="text-white font-bold text-lg md:text-xl">
                    {company.logo}
                  </span>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </div>

        {/* Stats Section */}
        <motion.div
          className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8 text-center"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div>
            <motion.div
              className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2"
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.5 }}
              viewport={{ once: true }}
            >
              500+
            </motion.div>
            <div className="text-ocean-600">Processes Automated</div>
          </div>
          <div>
            <motion.div
              className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2"
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              viewport={{ once: true }}
            >
              99.9%
            </motion.div>
            <div className="text-ocean-600">Uptime Guarantee</div>
          </div>
          <div>
            <motion.div
              className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2"
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.7 }}
              viewport={{ once: true }}
            >
              $10M+
            </motion.div>
            <div className="text-ocean-600">Cost Savings Generated</div>
          </div>
          <div>
            <motion.div
              className="text-3xl md:text-4xl font-bold text-ocean-900 mb-2"
              initial={{ opacity: 0, scale: 0.5 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.8 }}
              viewport={{ once: true }}
            >
              24/7
            </motion.div>
            <div className="text-ocean-600">Expert Support</div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
