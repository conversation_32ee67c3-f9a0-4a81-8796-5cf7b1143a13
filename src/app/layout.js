import "./globals.css";
import { Roboto } from "next/font/google";import "./globals.css";

const roboto = Roboto({
  variable: "--font-roboto",
  subsets: ["latin"],
  weight: ["400", "500", "700"],
  display: "swap",
});

export const metadata = {
  title: "MizuFlow - Intelligent Financial Automation",
  description: "Transform your financial operations with intelligent automation. MizuFlow delivers end-to-end automation, accounting, and financial services to streamline your business processes and drive growth.",
  keywords: "financial automation, accounting services, AI forecasting, business intelligence, invoice automation, financial reporting",
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>" }],
  creator: "<PERSON><PERSON><PERSON><PERSON>",
  publisher: "<PERSON><PERSON><PERSON><PERSON>",
  openGraph: {
    title: "MizuFlow - Intelligent Financial Automation",
    description: "Transform your financial operations with intelligent automation. Streamline your business processes and drive growth.",
    url: "https://mizuflow.ai",
    siteName: "MizuFlow",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "<PERSON><PERSON><PERSON><PERSON> - Intelligent Financial Automation",
    description: "Transform your financial operations with intelligent automation.",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en" className={roboto.variable}>
      <body className="font-sans antialiased">
        {children}
      </body>
    </html>
  );
}